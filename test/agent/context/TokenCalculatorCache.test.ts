import { TokenCalculator } from '../../../src/agent/context/TokenCalculator';

describe('TokenCalculator Cache Optimization', () => {
  let tokenCalculator: TokenCalculator;

  beforeEach(() => {
    tokenCalculator = new TokenCalculator('claude-3.5-sonnet', 'test-session', 'test-chat');
  });

  afterEach(() => {
    tokenCalculator.clearCache();
  });

  describe('Cache Basic Functionality', () => {
    it('should cache token calculation results', async () => {
      const text = 'This is a test text for token calculation';
      
      // 第一次计算
      const result1 = await tokenCalculator.calculate(text);
      
      // 第二次计算应该从缓存获取
      const result2 = await tokenCalculator.calculate(text);
      
      expect(result1).toBe(result2);
      
      const stats = tokenCalculator.getCacheStats();
      expect(stats.size).toBe(1);
    });

    it('should handle different texts correctly', async () => {
      const text1 = 'First text for testing';
      const text2 = 'Second text for testing';
      
      const result1 = await tokenCalculator.calculate(text1);
      const result2 = await tokenCalculator.calculate(text2);
      
      expect(result1).not.toBe(result2);
      
      const stats = tokenCalculator.getCacheStats();
      expect(stats.size).toBe(2);
    });
  });

  describe('Incremental Calculation', () => {
    it('should perform incremental calculation for similar texts', async () => {
      const baseText = 'This is a long text that will be used as base for incremental calculation. It contains multiple sentences and should be long enough to trigger the similarity matching algorithm.';
      const modifiedText = 'This is a long text that will be used as base for incremental calculation. It contains multiple sentences and should be long enough to trigger the similarity matching algorithm. This is an additional sentence.';
      
      // 先计算基础文本
      const baseResult = await tokenCalculator.calculate(baseText);
      
      // 计算修改后的文本，应该触发增量计算
      const modifiedResult = await tokenCalculator.calculate(modifiedText);
      
      expect(modifiedResult).toBeGreaterThan(baseResult);
      
      const stats = tokenCalculator.getCacheStats();
      expect(stats.size).toBe(2);
    });

    it('should handle text with common prefix and suffix', async () => {
      const prefix = 'This is a common prefix that appears in both texts. ';
      const suffix = ' This is a common suffix that appears in both texts.';
      const middle1 = 'This is the first middle part.';
      const middle2 = 'This is the second middle part with more content.';
      
      const text1 = prefix + middle1 + suffix;
      const text2 = prefix + middle2 + suffix;
      
      const result1 = await tokenCalculator.calculate(text1);
      const result2 = await tokenCalculator.calculate(text2);
      
      expect(result1).not.toBe(result2);
      expect(result2).toBeGreaterThan(result1); // text2 has more content
    });
  });

  describe('Cache Management', () => {
    it('should respect cache size limits', async () => {
      // 创建一个小容量的TokenCalculator来测试LRU行为
      const smallCacheCalculator = new TokenCalculator('claude-3.5-sonnet', 'test-session', 'test-chat');
      
      // 添加多个不同的文本，超过缓存容量
      const texts = [];
      for (let i = 0; i < 150; i++) { // 超过默认缓存大小100
        texts.push(`Test text number ${i} with unique content`);
      }
      
      for (const text of texts) {
        await smallCacheCalculator.calculate(text);
      }
      
      const stats = smallCacheCalculator.getCacheStats();
      expect(stats.size).toBeLessThanOrEqual(100); // 不应超过缓存容量
      
      smallCacheCalculator.clearCache();
    });

    it('should clear cache correctly', async () => {
      const text = 'Test text for cache clearing';
      await tokenCalculator.calculate(text);
      
      let stats = tokenCalculator.getCacheStats();
      expect(stats.size).toBe(1);
      
      tokenCalculator.clearCache();
      
      stats = tokenCalculator.getCacheStats();
      expect(stats.size).toBe(0);
    });
  });

  describe('Cache Statistics', () => {
    it('should provide accurate cache statistics', async () => {
      const text1 = 'First test text';
      const text2 = 'Second test text';
      
      await tokenCalculator.calculate(text1);
      await tokenCalculator.calculate(text2);
      
      const stats = tokenCalculator.getCacheStats();
      
      expect(stats.size).toBe(2);
      expect(stats.capacity).toBe(100);
      expect(stats.utilizationRate).toBe(0.02); // 2/100
      expect(stats.oldestKey).toBeDefined();
      expect(stats.newestKey).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should fallback gracefully when incremental calculation fails', async () => {
      // 这个测试需要模拟API调用失败的情况
      // 在实际实现中，可能需要使用jest.mock来模拟API失败
      const text = 'Test text for error handling';
      
      const result = await tokenCalculator.calculate(text);
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThan(0);
    });
  });
});
