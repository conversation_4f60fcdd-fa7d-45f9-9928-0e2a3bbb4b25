export interface FileContent {
  path: string;
  content: string;
  processedRanges: Array<{start: number, end: number}>;
  remainingRanges: Array<{start: number, end: number}>;
  importance: number;
  source: 'user' | 'tool';
}

export interface ContextState {
  maxTokens: number;
  systemPrompt: string;
  availableTokens?: number;
}

export interface TokenCalculator {
  calculate(text: string): Promise<number>;
  cacluateList(list: any[]): Promise<number>;
}

export interface ContextResult {
  messages: MessageParam[];
}