import { TokenCalculator as ITokenCalculator } from '../types/context';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import { Api } from '@/http';
import { PerfLoggerManager } from '@/util/perf-logger';
import * as crypto from 'crypto';

/**
 * 缓存条目接口
 */
interface CacheEntry {
  uid: string;
  text: string;
  tokenCount: number;
  timestamp: number;
}

export class TokenCalculator implements ITokenCalculator {
  private logger = new Logger('token-calculator');
  private model: string;
  private httpClient = new Api();
  private perfLogger;
  private sessionId: string;

  // 缓存相关属性
  private cache: Map<string, CacheEntry> = new Map();
  private readonly maxCacheSize: number = 1000; // 最大缓存条目数

  constructor(model: string, sessionId?: string, chatId?: string) {
    // 在初始化时确定使用的模型
    this.model = model;
    this.sessionId = sessionId || 'default';

    // 初始化性能记录器，优先使用 chatId 作为日志文件标识
    this.perfLogger = PerfLoggerManager.getInstance().getLogger(this.sessionId, chatId);
  }

  /**
   * 计算文本的token数量
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  async calculate(text: string): Promise<number> {
    // 为每个text生成一个唯一的uid，用作缓存使用
    const uid = this.generateUid(text);

    // 首先检查缓存
    const cachedResult = this.getCachedTokenCount(uid);
    if (cachedResult !== null) {
      this.logger.debug(`Using cached token count for text (uid: ${uid}): ${cachedResult}`);
      return cachedResult;
    }

    // === 性能统计：开始记录 token 计算 ===
    this.perfLogger.start('calculateTokens', 'tokenizer');

    try {
      // 调用token计算API
      const result = await this.callTokenizerApi(text);

      // 将结果存入缓存
      this.setCachedTokenCount(uid, text, result);

      // === 性能统计：成功结束记录 ===
      const duration = this.perfLogger.end('calculateTokens', 'tokenizer');

      // 记录成功的计算
      this.perfLogger.record('tokenCalculationSuccess', duration, 'tokenizer');

      return result;
    } catch (error) {
      this.logger.error('Failed to calculate tokens:', error);

      // 记录 API 失败，开始备用方案
      this.perfLogger.start('fallbackTokenEstimation', 'tokenizer');

      // 如果API调用失败，使用简单的字符数估算作为备用方案
      this.logger.info('Falling back to character-based token estimation');
      const fallbackResult = this.estimateTokens(text);

      // 将备用结果也存入缓存
      this.setCachedTokenCount(uid, text, fallbackResult);

      // 记录备用方案耗时
      const fallbackDuration = this.perfLogger.end('fallbackTokenEstimation', 'tokenizer');

      // === 性能统计：备用方案结束记录 ===
      const totalDuration = this.perfLogger.end('calculateTokens', 'tokenizer');

      // 记录备用方案使用
      this.perfLogger.milestone('tokenizer-fallback-used', totalDuration, {
        originalError: error instanceof Error ? error.message : String(error),
        fallbackDuration: fallbackDuration,
        textLength: text.length,
        estimatedTokens: fallbackResult
      });

      return fallbackResult;
    }
  }

  /**
   * 调用tokenizer API计算token数量
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  private async callTokenizerApi(text: string): Promise<number> {
    // === 性能统计：开始记录 tokenizer API 调用 ===
    this.perfLogger.start('callTokenizerApi', 'tokenizer');

    try {
      const payload = {
        model: this.model,
        text: text
      };

      // 记录请求准备阶段
      this.perfLogger.start('prepareTokenizerRequest', 'tokenizer');
      const s = Date.now();
      this.perfLogger.end('prepareTokenizerRequest', 'tokenizer');

      // 记录 API 请求阶段
      this.perfLogger.start('tokenizerApiRequest', 'tokenizer');
      const { data } = await this.httpClient.post<number[]>(
        '/eapi/kwaipilot/plugin/tokenizer',
        JSON.stringify(payload)
      );
      const apiDuration = this.perfLogger.end('tokenizerApiRequest', 'tokenizer');

      // 记录响应处理阶段
      this.perfLogger.start('processTokenizerResponse', 'tokenizer');
      const tokenCount = data.length;
      this.perfLogger.end('processTokenizerResponse', 'tokenizer');

      const totalDuration = Date.now() - s;
      this.logger.debug(`Token API response time: ${totalDuration} ms, token count: ${tokenCount}`);

      // 记录关键性能里程碑
      this.perfLogger.milestone('tokenizer-api-completed', totalDuration, {
        model: this.model,
        textLength: text.length,
        tokenCount: tokenCount,
        apiDuration: apiDuration,
        sessionId: this.sessionId
      });

      // === 性能统计：结束记录 tokenizer API 调用 ===
      const overallDuration = this.perfLogger.end('callTokenizerApi', 'tokenizer');

      // 如果耗时较长，记录警告
      if (overallDuration > 1000) {
        this.perfLogger.warn('callTokenizerApi', `Tokenizer API call took ${overallDuration}ms, which is longer than expected`);
      }

      return tokenCount;
    } catch (error) {
      // 记录错误性能
      this.perfLogger.error('callTokenizerApi', error);

      // === 性能统计：异常结束记录 ===
      this.perfLogger.end('callTokenizerApi', 'tokenizer');

      this.logger.error('Error calling tokenizer API:', error);
      throw error;
    }
  }

  /**
   * 使用简单的字符数估算token数量（作为备用方案）
   * @param text 需要估算token的文本
   * @returns 估算的token数量
   */
  private estimateTokens(text: string): number {
    // 简单的字符数估算，大约4个字符对应1个token
    return Math.ceil(text.length / 4);
  }

  /**
   * 为文本生成唯一的uid
   * @param text 文本内容
   * @returns 唯一的uid
   */
  private generateUid(text: string): string {
    // 使用SHA256哈希生成唯一标识符
    return crypto.createHash('sha256').update(text).digest('hex');
  }

  /**
   * 从缓存中获取token数量
   * @param uid 文本的唯一标识符
   * @returns 缓存的token数量，如果不存在返回null
   */
  private getCachedTokenCount(uid: string): number | null {
    const entry = this.cache.get(uid);
    if (entry) {
      // 更新访问时间
      entry.timestamp = Date.now();
      this.logger.debug(`Cache hit for uid: ${uid}`);
      return entry.tokenCount;
    }
    return null;
  }

  /**
   * 将token数量存入缓存
   * @param uid 文本的唯一标识符
   * @param text 文本内容
   * @param tokenCount token数量
   */
  private setCachedTokenCount(uid: string, text: string, tokenCount: number): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldestCacheEntry();
    }

    const entry: CacheEntry = {
      uid,
      text,
      tokenCount,
      timestamp: Date.now()
    };

    this.cache.set(uid, entry);
    this.logger.debug(`Cached token count for uid: ${uid}, count: ${tokenCount}`);
  }

  /**
   * 删除最旧的缓存条目
   */
  private evictOldestCacheEntry(): void {
    let oldestUid: string | null = null;
    let oldestTimestamp = Date.now();

    for (const [uid, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestUid = uid;
      }
    }

    if (oldestUid) {
      this.cache.delete(oldestUid);
      this.logger.debug(`Evicted oldest cache entry: ${oldestUid}`);
    }
  }

  async cacluateList(list: any[]): Promise<number> {
    const result = await Promise.all(list.map(item => this.calculate(JSON.stringify(item))));
    return result.reduce((sum, count) => sum + count, 0);
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.calculateCacheHitRate(),
      entries: Array.from(this.cache.values()).map(entry => ({
        uid: entry.uid,
        textLength: entry.text.length,
        tokenCount: entry.tokenCount,
        timestamp: entry.timestamp
      }))
    };
  }

  /**
   * 计算缓存命中率（简单实现）
   */
  private calculateCacheHitRate(): number {
    // 这里简化实现，实际应该记录命中和未命中次数
    return this.cache.size > 0 ? 0.8 : 0; // 示例值
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.logger.info('Token calculator cache cleared');
  }
}
