import { TokenCalculator as ITokenCalculator } from '../types/context';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import { Api } from '@/http';
import { PerfLoggerManager } from '@/util/perf-logger';

export class TokenCalculator implements ITokenCalculator {
  private logger = new Logger('token-calculator');
  private model: string;
  private httpClient = new Api();
  private perfLogger;
  private sessionId: string;

  constructor(model: string, sessionId?: string, chatId?: string) {
    // 在初始化时确定使用的模型
    this.model = model;
    this.sessionId = sessionId || 'default';

    // 初始化性能记录器，优先使用 chatId 作为日志文件标识
    this.perfLogger = PerfLoggerManager.getInstance().getLogger(this.sessionId, chatId);
  }

  /**
   * 计算文本的token数量
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  async calculate(text: string): Promise<number> {
    // 需要 给 每个生成一个 唯一的 uid ，用作缓存使用

    // === 性能统计：开始记录 token 计算 ===
    this.perfLogger.start('calculateTokens', 'tokenizer');

    try {
      // 调用token计算API
      const result = await this.callTokenizerApi(text);

      // === 性能统计：成功结束记录 ===
      const duration = this.perfLogger.end('calculateTokens', 'tokenizer');

      // 记录成功的计算
      this.perfLogger.record('tokenCalculationSuccess', duration, 'tokenizer');

      return result;
    } catch (error) {
      this.logger.error('Failed to calculate tokens:', error);

      // 记录 API 失败，开始备用方案
      this.perfLogger.start('fallbackTokenEstimation', 'tokenizer');

      // 如果API调用失败，使用简单的字符数估算作为备用方案
      this.logger.info('Falling back to character-based token estimation');
      const fallbackResult = this.estimateTokens(text);

      // 记录备用方案耗时
      const fallbackDuration = this.perfLogger.end('fallbackTokenEstimation', 'tokenizer');

      // === 性能统计：备用方案结束记录 ===
      const totalDuration = this.perfLogger.end('calculateTokens', 'tokenizer');

      // 记录备用方案使用
      this.perfLogger.milestone('tokenizer-fallback-used', totalDuration, {
        originalError: error instanceof Error ? error.message : String(error),
        fallbackDuration: fallbackDuration,
        textLength: text.length,
        estimatedTokens: fallbackResult
      });

      return fallbackResult;
    }
  }

  /**
   * 调用tokenizer API计算token数量
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  private async callTokenizerApi(text: string): Promise<number> {
    // === 性能统计：开始记录 tokenizer API 调用 ===
    this.perfLogger.start('callTokenizerApi', 'tokenizer');

    try {
      const payload = {
        model: this.model,
        text: text
      };

      // 记录请求准备阶段
      this.perfLogger.start('prepareTokenizerRequest', 'tokenizer');
      const s = Date.now();
      this.perfLogger.end('prepareTokenizerRequest', 'tokenizer');

      // 记录 API 请求阶段
      this.perfLogger.start('tokenizerApiRequest', 'tokenizer');
      const { data } = await this.httpClient.post<number[]>(
        '/eapi/kwaipilot/plugin/tokenizer',
        JSON.stringify(payload)
      );
      const apiDuration = this.perfLogger.end('tokenizerApiRequest', 'tokenizer');

      // 记录响应处理阶段
      this.perfLogger.start('processTokenizerResponse', 'tokenizer');
      const tokenCount = data.length;
      this.perfLogger.end('processTokenizerResponse', 'tokenizer');

      const totalDuration = Date.now() - s;
      this.logger.debug(`Token API response time: ${totalDuration} ms, token count: ${tokenCount}`);

      // 记录关键性能里程碑
      this.perfLogger.milestone('tokenizer-api-completed', totalDuration, {
        model: this.model,
        textLength: text.length,
        tokenCount: tokenCount,
        apiDuration: apiDuration,
        sessionId: this.sessionId
      });

      // === 性能统计：结束记录 tokenizer API 调用 ===
      const overallDuration = this.perfLogger.end('callTokenizerApi', 'tokenizer');

      // 如果耗时较长，记录警告
      if (overallDuration > 1000) {
        this.perfLogger.warn('callTokenizerApi', `Tokenizer API call took ${overallDuration}ms, which is longer than expected`);
      }

      return tokenCount;
    } catch (error) {
      // 记录错误性能
      this.perfLogger.error('callTokenizerApi', error);

      // === 性能统计：异常结束记录 ===
      this.perfLogger.end('callTokenizerApi', 'tokenizer');

      this.logger.error('Error calling tokenizer API:', error);
      throw error;
    }
  }

  /**
   * 使用简单的字符数估算token数量（作为备用方案）
   * @param text 需要估算token的文本
   * @returns 估算的token数量
   */
  private estimateTokens(text: string): number {
    // 简单的字符数估算，大约4个字符对应1个token
    return Math.ceil(text.length / 4);
  }

  async cacluateList(list: any[]): Promise<number> {
    const result = await Promise.all(list.map(item => this.calculate(JSON.stringify(item))));
    return result.reduce((sum, count) => sum + count, 0);
  }
}
