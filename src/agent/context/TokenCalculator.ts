import { TokenCalculator as ITokenCalculator } from '../types/context';
import { Logger } from '@/util/log';
import { Api } from '@/http';
import { PerfLoggerManager } from '@/util/perf-logger';
import { LRUCache } from '@/ui-preview/browser/core/lru-cache';

/**
 * 缓存条目接口
 */
interface CacheEntry {
  text: string;
  tokenCount: number;
  timestamp: number;
}

/**
 * 文本差异信息
 */
interface TextDiff {
  commonPrefix: string;
  commonSuffix: string;
  newMiddle: string;
  oldMiddle: string;
  prefixTokens: number;
  suffixTokens: number;
}

export class TokenCalculator implements ITokenCalculator {
  private logger = new Logger('token-calculator');
  private model: string;
  private httpClient = new Api();
  private perfLogger;
  private sessionId: string;

  // 缓存相关属性
  private cache: LRUCache<CacheEntry>;
  private readonly cacheSize: number = 100; // 缓存最多100个条目
  private readonly similarityThreshold: number = 0.8; // 相似度阈值

  constructor(model: string, sessionId?: string, chatId?: string) {
    // 在初始化时确定使用的模型
    this.model = model;
    this.sessionId = sessionId || 'default';

    // 初始化性能记录器，优先使用 chatId 作为日志文件标识
    this.perfLogger = PerfLoggerManager.getInstance().getLogger(this.sessionId, chatId);

    // 初始化缓存
    this.cache = new LRUCache<CacheEntry>(this.cacheSize);
  }

  /**
   * 计算文本的token数量（带缓存优化）
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  async calculate(text: string): Promise<number> {
    // === 性能统计：开始记录 token 计算 ===
    this.perfLogger.start('calculateTokens', 'tokenizer');

    try {
      // 1. 检查精确缓存匹配
      const cacheKey = this.generateCacheKey(text);
      const exactMatch = this.cache.get(cacheKey);

      if (exactMatch && exactMatch.text === text) {
        this.logger.debug('Cache hit: exact match found');
        this.perfLogger.end('calculateTokens', 'tokenizer');
        return exactMatch.tokenCount;
      }

      // 2. 查找相似的缓存条目进行增量计算
      // const similarMatch = this.findMostSimilarCacheEntry(text);

      // if (similarMatch) {
      //   this.logger.debug(`Cache hit: similar match found with similarity ${similarMatch.similarity}`);

      //   // 分析文本差异
      //   const diff = this.analyzeTextDiff(similarMatch.entry.text, text);

      //   // 如果差异很小，尝试增量计算
      //   if (diff.newMiddle.length < text.length * 0.3) { // 差异部分小于30%
      //     try {
      //       const result = await this.calculateIncremental(similarMatch.entry, diff);

      //       // 缓存新结果
      //       this.cache.set(cacheKey, {
      //         text,
      //         tokenCount: result,
      //         timestamp: Date.now()
      //       });

      //       this.perfLogger.end('calculateTokens', 'tokenizer');
      //       return result;
      //     } catch (error) {
      //       this.logger.warn('Incremental calculation failed, falling back to full calculation', error);
      //     }
      //   }
      // }

      // 3. 执行完整计算
      const result = await this.callTokenizerApi(text);

      // 缓存结果
      this.cache.set(cacheKey, {
        text,
        tokenCount: result,
        timestamp: Date.now()
      });

      // === 性能统计：成功结束记录 ===
      const duration = this.perfLogger.end('calculateTokens', 'tokenizer');

      // 记录成功的计算
      this.perfLogger.record('tokenCalculationSuccess', duration, 'tokenizer');

      return result;
    } catch (error) {
      this.logger.error('Failed to calculate tokens:', error);

      // 记录 API 失败，开始备用方案
      this.perfLogger.start('fallbackTokenEstimation', 'tokenizer');

      // 如果API调用失败，使用简单的字符数估算作为备用方案
      this.logger.info('Falling back to character-based token estimation');
      const fallbackResult = this.estimateTokens(text);

      // 记录备用方案耗时
      const fallbackDuration = this.perfLogger.end('fallbackTokenEstimation', 'tokenizer');

      // === 性能统计：备用方案结束记录 ===
      const totalDuration = this.perfLogger.end('calculateTokens', 'tokenizer');

      // 记录备用方案使用
      this.perfLogger.milestone('tokenizer-fallback-used', totalDuration, {
        originalError: error instanceof Error ? error.message : String(error),
        fallbackDuration: fallbackDuration,
        textLength: text.length,
        estimatedTokens: fallbackResult
      });

      return fallbackResult;
    }
  }

  /**
   * 调用tokenizer API计算token数量
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  private async callTokenizerApi(text: string): Promise<number> {
    // === 性能统计：开始记录 tokenizer API 调用 ===
    this.perfLogger.start('callTokenizerApi', 'tokenizer');

    try {
      const payload = {
        model: this.model,
        text: text
      };

      // 记录请求准备阶段
      this.perfLogger.start('prepareTokenizerRequest', 'tokenizer');
      const s = Date.now();
      this.perfLogger.end('prepareTokenizerRequest', 'tokenizer');

      // 记录 API 请求阶段
      this.perfLogger.start('tokenizerApiRequest', 'tokenizer');
      const { data } = await this.httpClient.post<number[]>(
        '/eapi/kwaipilot/plugin/tokenizer',
        JSON.stringify(payload)
      );
      const apiDuration = this.perfLogger.end('tokenizerApiRequest', 'tokenizer');

      // 记录响应处理阶段
      this.perfLogger.start('processTokenizerResponse', 'tokenizer');
      const tokenCount = data.length;
      this.perfLogger.end('processTokenizerResponse', 'tokenizer');

      const totalDuration = Date.now() - s;
      this.logger.debug(`Token API response time: ${totalDuration} ms, token count: ${tokenCount}`);

      // 记录关键性能里程碑
      this.perfLogger.milestone('tokenizer-api-completed', totalDuration, {
        model: this.model,
        textLength: text.length,
        tokenCount: tokenCount,
        apiDuration: apiDuration,
        sessionId: this.sessionId
      });

      // === 性能统计：结束记录 tokenizer API 调用 ===
      const overallDuration = this.perfLogger.end('callTokenizerApi', 'tokenizer');

      // 如果耗时较长，记录警告
      if (overallDuration > 1000) {
        this.perfLogger.warn('callTokenizerApi', `Tokenizer API call took ${overallDuration}ms, which is longer than expected`);
      }

      return tokenCount;
    } catch (error) {
      // 记录错误性能
      this.perfLogger.error('callTokenizerApi', error);

      // === 性能统计：异常结束记录 ===
      this.perfLogger.end('callTokenizerApi', 'tokenizer');

      this.logger.error('Error calling tokenizer API:', error);
      throw error;
    }
  }

  /**
   * 使用简单的字符数估算token数量（作为备用方案）
   * @param text 需要估算token的文本
   * @returns 估算的token数量
   */
  private estimateTokens(text: string): number {
    // 简单的字符数估算，大约4个字符对应1个token
    return Math.ceil(text.length / 4);
  }

  /**
   * 生成文本的缓存键
   * @param text 文本内容
   * @returns 缓存键
   */
  private generateCacheKey(text: string): string {
    // 使用文本长度和前后部分内容生成键，避免完整文本作为键
    const maxKeyLength = 200;
    if (text.length <= maxKeyLength) {
      return text;
    }

    const prefix = text.substring(0, 100);
    const suffix = text.substring(text.length - 100);
    return `${prefix}...${suffix}[${text.length}]`;
  }

  /**
   * 计算两个文本的相似度
   * @param text1 文本1
   * @param text2 文本2
   * @returns 相似度 (0-1)
   */
  private calculateSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 1;
    if (text1.length === 0 || text2.length === 0) return 0;

    // 使用简单的编辑距离算法计算相似度
    const maxLength = Math.max(text1.length, text2.length);
    const distance = this.levenshteinDistance(text1, text2);
    return 1 - (distance / maxLength);
  }

  /**
   * 计算编辑距离
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    // 初始化矩阵
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    // 填充矩阵
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // 替换
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j] + 1      // 删除
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 分析两个文本的差异，找出公共前缀和后缀
   * @param oldText 旧文本
   * @param newText 新文本
   * @returns 文本差异信息
   */
  private analyzeTextDiff(oldText: string, newText: string): TextDiff {
    // 找公共前缀
    let prefixEnd = 0;
    const minLength = Math.min(oldText.length, newText.length);
    while (prefixEnd < minLength && oldText[prefixEnd] === newText[prefixEnd]) {
      prefixEnd++;
    }

    // 找公共后缀
    let suffixStart = 0;
    const oldRemaining = oldText.length - prefixEnd;
    const newRemaining = newText.length - prefixEnd;
    const maxSuffixLength = Math.min(oldRemaining, newRemaining);

    while (suffixStart < maxSuffixLength &&
      oldText[oldText.length - 1 - suffixStart] === newText[newText.length - 1 - suffixStart]) {
      suffixStart++;
    }

    const commonPrefix = oldText.substring(0, prefixEnd);
    const commonSuffix = suffixStart > 0 ? oldText.substring(oldText.length - suffixStart) : '';

    const oldMiddleStart = prefixEnd;
    const oldMiddleEnd = oldText.length - suffixStart;
    const newMiddleStart = prefixEnd;
    const newMiddleEnd = newText.length - suffixStart;

    const oldMiddle = oldText.substring(oldMiddleStart, oldMiddleEnd);
    const newMiddle = newText.substring(newMiddleStart, newMiddleEnd);

    return {
      commonPrefix,
      commonSuffix,
      oldMiddle,
      newMiddle,
      prefixTokens: 0, // 将在后续计算中填充
      suffixTokens: 0  // 将在后续计算中填充
    };
  }

  /**
   * 在缓存中查找最相似的条目
   * @param text 目标文本
   * @returns 最相似的缓存条目和相似度，如果没有找到返回null
   */
  private findMostSimilarCacheEntry(text: string): { entry: CacheEntry; similarity: number; key: string } | null {
    let bestMatch: { entry: CacheEntry; similarity: number; key: string } | null = null;
    let bestSimilarity = 0;

    // 遍历缓存中的所有条目
    for (const key of this.cache.keys()) {
      const entry = this.cache.get(key);
      if (!entry) continue;

      const similarity = this.calculateSimilarity(text, entry.text);
      if (similarity > bestSimilarity && similarity >= this.similarityThreshold) {
        bestSimilarity = similarity;
        bestMatch = { entry, similarity, key };
      }
    }

    return bestMatch;
  }

  /**
   * 基于缓存条目和文本差异进行增量token计算
   * @param cachedEntry 缓存的条目
   * @param diff 文本差异信息
   * @returns 新文本的token数量
   */
  private async calculateIncremental(cachedEntry: CacheEntry, diff: TextDiff): Promise<number> {
    // 如果没有差异，直接返回缓存的token数
    if (diff.newMiddle === diff.oldMiddle) {
      return cachedEntry.tokenCount;
    }

    // 计算公共前缀和后缀的token数（如果还没有计算过）
    if (diff.prefixTokens === 0 && diff.commonPrefix.length > 0) {
      diff.prefixTokens = await this.callTokenizerApi(diff.commonPrefix);
    }

    if (diff.suffixTokens === 0 && diff.commonSuffix.length > 0) {
      diff.suffixTokens = await this.callTokenizerApi(diff.commonSuffix);
    }

    // 计算新的中间部分的token数
    let newMiddleTokens = 0;
    if (diff.newMiddle.length > 0) {
      newMiddleTokens = await this.callTokenizerApi(diff.newMiddle);
    }

    // 计算旧的中间部分的token数
    let oldMiddleTokens = 0;
    if (diff.oldMiddle.length > 0) {
      oldMiddleTokens = await this.callTokenizerApi(diff.oldMiddle);
    }

    // 增量计算：原始token数 - 旧中间部分token数 + 新中间部分token数
    const newTokenCount = cachedEntry.tokenCount - oldMiddleTokens + newMiddleTokens;

    this.logger.debug('Incremental calculation completed', {
      originalTokens: cachedEntry.tokenCount,
      oldMiddleTokens,
      newMiddleTokens,
      finalTokens: newTokenCount,
      prefixTokens: diff.prefixTokens,
      suffixTokens: diff.suffixTokens
    });

    return newTokenCount;
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  getCacheStats() {
    return {
      ...this.cache.getStats(),
      cacheHitRate: this.calculateCacheHitRate(),
      oldestEntry: this.getOldestCacheEntry(),
      newestEntry: this.getNewestCacheEntry()
    };
  }

  /**
   * 计算缓存命中率（简单实现，实际项目中可能需要更复杂的统计）
   */
  private calculateCacheHitRate(): number {
    // 这里简化实现，实际应该记录命中和未命中次数
    return this.cache.size() > 0 ? 0.8 : 0; // 示例值
  }

  /**
   * 获取最旧的缓存条目
   */
  private getOldestCacheEntry(): CacheEntry | null {
    const oldestKeys = this.cache.getLRUKeys(1);
    if (oldestKeys.length === 0) return null;

    return this.cache.get(oldestKeys[0]) || null;
  }

  /**
   * 获取最新的缓存条目
   */
  private getNewestCacheEntry(): CacheEntry | null {
    const newestKeys = this.cache.getMRUKeys(1);
    if (newestKeys.length === 0) return null;

    return this.cache.get(newestKeys[0]) || null;
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.logger.info('Token calculator cache cleared');
  }
}
